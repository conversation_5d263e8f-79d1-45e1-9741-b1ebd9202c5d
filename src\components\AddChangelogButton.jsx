import React, { useState } from 'react';
import { addChangelogEntry, CHANGELOG_CATEGORIES } from '../services/changelog';
import { testFirebaseConnection, testAddChangelogEntry } from '../utils/firebaseTest';

const AddChangelogButton = () => {
  const [isAdding, setIsAdding] = useState(false);
  const [status, setStatus] = useState('');
  const [isTesting, setIsTesting] = useState(false);

  const testFirebase = async () => {
    setIsTesting(true);
    setStatus('Testing Firebase connection...');

    try {
      const result = await testFirebaseConnection();
      setStatus(`🔍 ${result.message} (${result.entriesCount || 0} entries found)`);
    } catch (error) {
      setStatus('❌ Firebase test failed: ' + error.message);
    } finally {
      setIsTesting(false);
    }
  };

  const addCriticalFixesEntry = async () => {
    setIsAdding(true);
    setStatus('Adding changelog entry...');

    try {
      const newEntry = {
        version: '1.1.1',
        title: 'Critical Bug Fixes - Story Submission & Mobile Navigation 🔧',
        description: 'We\'ve fixed several important issues that were affecting your experience on NAROOP. Story sharing now works smoothly, and mobile navigation is much improved!',
        category: CHANGELOG_CATEGORIES.BUGFIX,
        releaseDate: new Date().toISOString(),
        changes: [
          'Fixed story submission errors - you can now share your stories without permission issues',
          'Improved mobile menu - all navigation options are now visible and scrollable on mobile devices',
          'Enhanced form validation with clearer error messages',
          'Better authentication handling to prevent login-related issues',
          'Improved mobile touch targets for easier navigation on phones and tablets',
          'Fixed duplicate Kids Zone display issue for cleaner navigation',
          'Added better error handling throughout the platform',
          'Enhanced security with updated Firebase rules'
        ]
      };

      await addChangelogEntry(newEntry);
      setStatus('✅ Successfully added v1.1.1 changelog entry!');
      
      // Also add the v1.1.0 entry if it doesn't exist
      const v110Entry = {
        version: '1.1.0',
        title: 'Fresh New Look - Green Theme Update! 🌱',
        description: 'We\'ve given NAROOP a beautiful new look! The login page and overall design now features a fresh green color scheme that represents growth, prosperity, and positive energy.',
        category: CHANGELOG_CATEGORIES.IMPROVEMENT,
        releaseDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
        changes: [
          'Updated login page with a beautiful green color theme',
          'Replaced orange and purple colors with calming green tones',
          'New green background gradients throughout the platform',
          'Improved visual consistency across all pages',
          'Enhanced user experience with nature-inspired colors',
          'Better color accessibility and readability'
        ]
      };

      await addChangelogEntry(v110Entry);
      setStatus('✅ Successfully added both changelog entries! Refresh the changelog to see them.');
      
    } catch (error) {
      console.error('Error adding changelog entry:', error);
      setStatus('❌ Error: ' + error.message);
    } finally {
      setIsAdding(false);
    }
  };

  return (
    <div style={{ 
      position: 'fixed', 
      top: '20px', 
      right: '20px', 
      zIndex: 9999,
      background: 'white',
      padding: '15px',
      borderRadius: '8px',
      boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
      border: '2px solid #e63946'
    }}>
      <h4 style={{ margin: '0 0 10px 0', color: '#e63946' }}>Admin: Add Changelog</h4>

      <button
        onClick={testFirebase}
        disabled={isTesting}
        style={{
          background: isTesting ? '#ccc' : '#2196F3',
          color: 'white',
          border: 'none',
          padding: '6px 12px',
          borderRadius: '4px',
          cursor: isTesting ? 'not-allowed' : 'pointer',
          marginBottom: '8px',
          display: 'block',
          width: '100%',
          fontSize: '12px'
        }}
      >
        {isTesting ? 'Testing...' : 'Test Firebase Connection'}
      </button>

      <button
        onClick={addCriticalFixesEntry}
        disabled={isAdding}
        style={{
          background: isAdding ? '#ccc' : '#e63946',
          color: 'white',
          border: 'none',
          padding: '8px 16px',
          borderRadius: '4px',
          cursor: isAdding ? 'not-allowed' : 'pointer',
          marginBottom: '10px',
          display: 'block',
          width: '100%'
        }}
      >
        {isAdding ? 'Adding...' : 'Add v1.1.1 Changelog'}
      </button>
      {status && (
        <p style={{ 
          margin: '0', 
          fontSize: '12px', 
          color: status.includes('✅') ? 'green' : status.includes('❌') ? 'red' : 'blue',
          wordWrap: 'break-word',
          maxWidth: '250px'
        }}>
          {status}
        </p>
      )}
    </div>
  );
};

export default AddChangelogButton;
