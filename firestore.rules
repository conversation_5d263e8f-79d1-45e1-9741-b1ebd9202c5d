rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read and write their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Stories collection rules
    match /stories/{storyId} {
      // Anyone can read published stories
      allow read: if resource.data.isPublished == true;
      
      // Only authenticated users can create stories
      allow create: if request.auth != null 
        && request.auth.uid == request.resource.data.author
        && request.resource.data.author is string
        && request.resource.data.title is string
        && request.resource.data.content is string;
      
      // Only the author can update or delete their own stories
      allow update, delete: if request.auth != null 
        && request.auth.uid == resource.data.author;
    }
    
    // Support requests - users can create and read their own
    match /supportRequests/{requestId} {
      allow read, write: if request.auth != null 
        && (request.auth.uid == resource.data.authorId 
            || request.auth.uid == request.resource.data.authorId);
      allow read: if request.auth != null; // Allow reading all for community support
    }
    
    // Activism campaigns - users can create and read
    match /activismCampaigns/{campaignId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null 
        && request.auth.uid == request.resource.data.creatorId;
      allow update: if request.auth != null 
        && request.auth.uid == resource.data.creatorId;
    }
    
    // Notifications - users can read their own notifications
    match /notifications/{notificationId} {
      allow read, write: if request.auth != null 
        && request.auth.uid == resource.data.userId;
    }
    
    // Messages - users can read/write messages they're part of
    match /messages/{messageId} {
      allow read, write: if request.auth != null 
        && (request.auth.uid in resource.data.participants 
            || request.auth.uid in request.resource.data.participants);
    }
    
    // Connections - users can manage their own connections
    match /connections/{connectionId} {
      allow read, write: if request.auth != null 
        && (request.auth.uid == resource.data.fromUserId 
            || request.auth.uid == resource.data.toUserId
            || request.auth.uid == request.resource.data.fromUserId 
            || request.auth.uid == request.resource.data.toUserId);
    }
    
    // Default deny rule for any other collections
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
